<script lang="ts">
	import { Button, H1, Input, P1 } from '$lib/ui';
	import H2 from '$lib/ui/typography/H2.svelte';
	import Dropdown from '$lib/ui/Dropdown.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client } from 'sveltekit-superforms/adapters';
	import { Turnstile } from 'svelte-turnstile';
	import { schema, zodSATDates } from '$lib/schema';
	import { PUBLIC_TURNSTILE_SITE_KEY } from '$env/static/public';
	import { dev } from '$app/environment';
	import Hero from '$lib/landingPage/Hero.svelte';
	import SectionWrapper from '$lib/landingPage/SectionWrapper.svelte';

	let { data } = $props();

	// Call this to reset the turnstile
	let reset = $state<() => void>();

	// Client API:
	const { form, errors, message, constraints, enhance } = superForm(data.form, {
		validators: zod4Client(schema),
		onUpdate() {
			reset?.();
		}
	});
</script>

<svelte:head>
	<title>DSAT16 Webinar</title>
</svelte:head>

{#snippet logo()}
	<div class="logo">
		<img src="/dsat16_logo.png" alt="dsat16 logo" />
		<H1>DSAT16</H1>
	</div>
{/snippet}

{#if !$message}
<SectionWrapper --bg-color="var(--white)" --padding-top="4rem" --padding-bottom="4rem">
		<div class="content-wrapper">
			<div class="content-left">
				{@render logo()}
				<P1>  09/09/2025 | 8 PM</P1>
				<H2 --text-color="var(--rose)">Cách tự học để đạt 1600 SAT</H2>
				<P1>Mọi người đều nghĩ tự học SAT rất khó và không thể đạt điểm cao. Nhưng thực tế hoàn toàn không như vậy.</P1>
				<P1>Trong buổi học 60 phút này, các em sẽ biết đến những chiến thuật tự học mà anh từng áp dụng để đạt được 1600 1st take mà ko bị mất tập trung và nản chí.</P1>
				<P1 isBold>Đây là các bước mà học sinh của DSAT16 áp dụng trong lúc ôn thi SAT và cuối cùng đạt điểm cao (1 bạn đạt 1600).</P1>
			</div>
			<form method="POST" class="content-right" use:enhance>
				<div class="inline">
					<Input fullWidth placeholder="Tên" label="Họ và tên" name="name" bind:value={$form.name} error={$errors.name?.[0]} constraints={$constraints.name} />
					<Input fullWidth placeholder="Email" label="Email" name="email" type="email" bind:value={$form.email} error={$errors.email?.[0]} constraints={$constraints.email} />
				</div>

				<div class="inline">
					<Input fullWidth placeholder="Điền 13 nếu đã tốt nghiệp" label="Lớp" name="grade" type="number" bind:value={$form.grade} error={$errors.grade?.[0]} constraints={$constraints.grade} />
					<Dropdown
						fullWidth
						label="Ngày thi dự kiến"
						placeholder="Chọn ngày thi"
						bind:dropdownText={$form.test_date}
						dropdownChoices={[...zodSATDates]}
						name="test_date"
						error={$errors.test_date?.[0]}
						constraints={$constraints.test_date}
					/>
				</div>

				<div class="inline">
					<Input fullWidth label="Điểm SAT hiện tại" name="current_sat" type="number" bind:value={$form.current_sat} error={$errors.current_sat?.[0]} constraints={$constraints.current_sat} />
					<Input fullWidth label="Điểm SAT mong muốn" name="target_sat" type="number" bind:value={$form.target_sat} error={$errors.target_sat?.[0]} constraints={$constraints.target_sat} />
				</div>

				{#if !dev}
				<Turnstile siteKey={PUBLIC_TURNSTILE_SITE_KEY} bind:reset on:callback={(e) => $form['cf-turnstile-response'] = e.detail.token} />
				{/if}

				<Button type="submit">Đăng ký</Button>
			</form>
		</div>
</SectionWrapper>

<SectionWrapper --bg-color="var(--white)" --padding-top="4rem" --padding-bottom="4rem">
	<H2>Tự xây dựng lộ trình học tập cá nhân một cách thú vị</H2>
</SectionWrapper>
{:else}
<section class="confirmation">
	{@render logo()}
	<div class="confirmation-text">
		<H2>Đăng ký thành công!</H2>
		<P1>DSAT16 đã gửi link webinar đến email của bạn. Bấm vào link để vào webinar vào lúc</P1>
		<P1>...</P1>
	</div>
</section>

<Hero />
{/if}

<style>
	section {
		padding: 4rem;
        width: 100%;
        border-bottom: 0.25rem solid var(--pitch-black);
	} 

    .content-wrapper {
        display: flex;
        width: 100%;
        max-width: 1440px;
		gap: 2rem;
    }

	.logo {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
	}

	.logo img {
		width: 3rem;
	}

	.content-left {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.content-right {
		display: flex;
		flex-direction: column;
		gap: 1rem;

		background: var(--light-aquamarine);
		border: 3px solid var(--pitch-black);
		border-radius: 1.25rem;
		padding: 4rem;
		box-shadow: 1.25rem 1.25rem 0 var(--pitch-black);
		min-width: 37.5rem;
		height: fit-content;
	}

	.content-left, .content-right {
		flex: 1 1 0;
	}

	.inline {
		display: flex;
		gap: 2rem;
	}

	.confirmation {
		text-align: center;
		width: 100%;
		background-color: var(--aquamarine);
	}

	.confirmation-text {
		margin-top: 2rem;
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	@media (max-width: 1024px) {
		.content-wrapper {
			flex-direction: column;
		}
	}

    @media (max-width: 768px) {
        section {
            padding: 4rem 1rem;
        }

		.inline {
			flex-direction: column;
			gap: 1rem;
		}

		.content-right {
			padding: 2rem;
			min-width: 0;
			box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		}
    }
</style>